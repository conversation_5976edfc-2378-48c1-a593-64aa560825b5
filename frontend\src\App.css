.App {
  min-height: 100vh;
  padding: 20px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 15px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.input-group small {
  display: block;
  margin-top: 5px;
  color: #6c757d;
  font-size: 12px;
}

.input-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.3s ease;
}

.input-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-bottom: 15px;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
  margin: 15px 0;
  font-size: 14px;
}

.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #667eea;
  font-weight: 600;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.result-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-top: 20px;
  border-left: 4px solid #28a745;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.result-card h3 {
  margin-top: 0;
  margin-bottom: 25px;
  color: #495057;
  text-align: center;
  font-size: 1.4rem;
}

.confidence-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
}

.confidence-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 1s ease-out;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
  position: relative;
}

.header h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 20px;
}

.status {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.result-item {
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item strong {
  color: #495057;
  margin-right: 10px;
}

.prediction {
  display: inline-block;
  font-size: 1.1rem;
  font-weight: 600;
  margin-left: 10px;
  padding: 6px 16px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.prediction:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.examples {
  display: grid;
  gap: 20px;
}

.example-group {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.example-group h4 {
  margin-bottom: 15px;
  color: #495057;
}

.example-btn {
  background: white;
  border: 2px solid #dee2e6;
  padding: 10px 16px;
  margin: 5px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.example-btn:hover {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateY(-1px);
}

.footer {
  text-align: center;
  margin-top: 40px;
  color: white;
  opacity: 0.8;
}

.footer p {
  font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .header h1 {
    font-size: 2.2rem;
  }

  .header p {
    font-size: 1rem;
  }

  .card {
    padding: 16px;
    margin-bottom: 15px;
  }

  .example-btn {
    display: block;
    width: 100%;
    margin: 5px 0;
  }
}

@media (min-width: 769px) and (max-width: 1199px) {
  .container {
    max-width: 900px;
    padding: 0 20px;
  }
}

/* Animation for results */
.result-card {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Confidence bar animation */
.confidence-fill {
  animation: fillBar 1s ease-out;
}

@keyframes fillBar {
  from {
    width: 0%;
  }
}

/* Loading animation improvements */
.loading {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Model Selection Styles */
.model-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.model-option {
  position: relative;
  border-radius: 8px;
  border: 2px solid #e1e5e9;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.model-option:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.model-option.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.model-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.model-label {
  display: flex;
  align-items: center;
  padding: 10px 14px;
  cursor: pointer;
  margin: 0;
  font-weight: normal;
}

.model-option.selected .model-label {
  color: white;
}

.model-emoji {
  font-size: 18px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.model-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.model-name {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.1;
}

.model-description {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.1;
}

.model-option.selected .model-description {
  opacity: 0.9;
}

/* Responsive adjustments for model selection */
@media (max-width: 768px) {
  .model-label {
    padding: 8px 12px;
  }
  
  .model-emoji {
    font-size: 16px;
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
  
  .model-name {
    font-size: 13px;
  }
  
  .model-description {
    font-size: 11px;
  }
}

/* Tab Navigation Styles */
.tab-navigation {
  display: flex;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
  backdrop-filter: blur(10px);
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: white;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* CSV Upload Styles */
.file-input {
  width: 100%;
  padding: 12px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.file-input:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.csv-config {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 20px 0;
}

.text-input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.text-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.select-input {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.select-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Progress Styles */
.progress-section {
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.progress-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
  font-size: 14px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* CSV Results Styles */
.csv-results {
  margin-top: 20px;
}

.results-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  padding: 8px 16px;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #5a6268;
}

.results-table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  font-size: 14px;
}

.results-table th {
  background: #667eea;
  color: white;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  white-space: nowrap;
}

.results-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: top;
}

.results-table tr:hover {
  background: #f8f9fa;
}

.text-cell {
  max-width: 200px;
  word-wrap: break-word;
  line-height: 1.4;
}

.table-note {
  padding: 10px;
  text-align: center;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 13px;
  border-top: 1px solid #dee2e6;
}

/* Responsive adjustments for CSV features */
@media (max-width: 768px) {
  .csv-config {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .progress-info {
    grid-template-columns: 1fr;
  }

  .results-header {
    flex-direction: column;
    align-items: stretch;
  }

  .results-summary {
    grid-template-columns: 1fr;
  }

  .results-table th,
  .results-table td {
    padding: 8px 6px;
    font-size: 13px;
  }

  .text-cell {
    max-width: 150px;
  }
}

/* Batch Size Slider Styles */
.batch-size-slider {
  margin: 12px 0;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e1e5e9;
  outline: none;
  -webkit-appearance: none;
  margin: 8px 0;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  margin-bottom: 12px;
}

.slider-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.slider-label.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  font-weight: 600;
  transform: scale(1.1);
}

.batch-size-display {
  text-align: center;
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  color: #495057;
}

.batch-size-display strong {
  color: #667eea;
  font-size: 16px;
}

/* Mobile responsive for slider */
@media (max-width: 768px) {
  .slider-labels {
    flex-wrap: wrap;
    gap: 4px;
    justify-content: center;
  }
  
  .slider-label {
    font-size: 11px;
    padding: 2px 4px;
  }
  
  .batch-size-display {
    font-size: 13px;
  }
  
  .batch-size-display strong {
    font-size: 15px;
  }
}

/* Model Configuration Container */
.model-config-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: stretch;
}

.model-selection-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 2px solid #e9ecef;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.model-selection-section .input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}

.model-selection-section .input-group label {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.model-selection {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.model-variant-selection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 2px solid #e9ecef;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-top: 2px solid #e9ecef;
  margin-top: 0;
  padding-top: 20px;
}

.model-variant-selection .input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}

.model-variant-selection .input-group label {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.variant-selection-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .model-config-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .model-selection-section,
  .model-variant-selection {
    height: auto;
  }
}

/* Temperature Control */
.temperature-control {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 2px solid #e9ecef;
}

.temperature-info {
  margin-bottom: 15px;
}

.temperature-info small {
  color: #6c757d;
  font-style: italic;
}

.temperature-input-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 15px 0;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.temperature-value-input {
  display: flex;
  align-items: center;
  gap: 10px;
}

.temperature-value-input label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0;
  min-width: 45px;
}

.temperature-number-input {
  width: 80px;
  padding: 6px 8px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  background: #fff;
}

.temperature-number-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.temperature-number-input:invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.temperature-range {
  display: flex;
  align-items: center;
}

.range-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 12px;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 15px 0;
}

.temperature-slider {
  flex: 1;
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #ff6b6b, #feca57, #48dbfb, #0abde3);
  outline: none;
  cursor: pointer;
}

.temperature-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #fff;
  border: 3px solid #667eea;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.temperature-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.temperature-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #fff;
  border: 3px solid #667eea;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.temperature-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.slider-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.temperature-description {
  text-align: center;
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 20px;
  margin-top: 10px;
  font-size: 13px;
  background: #e9ecef;
  color: #495057;
  transition: all 0.3s ease;
}

/* Temperature mode specific styling */
.temperature-description {
  background: #e8f4f8;
  color: #0984e3;
}

.temperature-description.high-confidence {
  background: #ffe8e8;
  color: #d63031;
}

.temperature-description.balanced {
  background: #e8f4f8;
  color: #0984e3;
}

.temperature-description.exploratory {
  background: #f0e8ff;
  color: #6c5ce7;
}

/* Responsive design for temperature control */
@media (max-width: 768px) {
  .temperature-input-section {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .temperature-value-input {
    justify-content: center;
  }
  
  .temperature-number-input {
    width: 100px;
  }
  
  .range-label {
    text-align: center;
  }
}

/* Result Grid Layout */
.result-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: start;
}

@media (max-width: 968px) {
  .result-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.result-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-chart h4 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #495057;
  text-align: center;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-container:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.chart-container:hover .chart-overlay {
  opacity: 1;
}

.expand-icon {
  font-size: 2rem;
  color: white;
  margin-bottom: 8px;
}

.expand-text {
  color: white;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}

/* Chart Modal Styles */
.chart-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.chart-modal {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  max-width: 1200px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideInScale 0.3s ease-out;
}

.chart-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.chart-modal-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.5rem;
}

.modal-close-btn {
  background: #6c757d;
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.modal-close-btn:hover {
  background: #495057;
  transform: scale(1.1);
}

.chart-modal-content {
  padding: 24px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

.modal-chart-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.modal-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.modal-info-item strong {
  color: #495057;
  min-width: 80px;
}

.modal-chart-container {
  height: 400px;
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-scores-table {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.modal-scores-table h4 {
  margin: 0 0 20px 0;
  color: #495057;
  text-align: center;
}

.scores-grid {
  display: grid;
  gap: 12px;
}

.score-item {
  background: white;
  border-radius: 6px;
  padding: 12px 16px;
  border-left: 4px solid #6c757d;
  transition: all 0.3s ease;
}

.score-item.best-score {
  border-left-color: #28a745;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.score-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
}

.best-badge {
  background: #28a745;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 600;
}

.score-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #495057;
  margin-bottom: 8px;
}

.score-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.8s ease-out;
}

/* Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive Modal */
@media (max-width: 768px) {
  .chart-modal {
    margin: 10px;
    max-height: 95vh;
  }
  
  .chart-modal-header {
    padding: 16px 20px;
  }
  
  .chart-modal-header h3 {
    font-size: 1.3rem;
  }
  
  .chart-modal-content {
    padding: 20px;
  }
  
  .modal-chart-container {
    height: 300px;
    padding: 12px;
  }
  
  .modal-chart-info {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .expand-icon {
    font-size: 1.5rem;
  }
  
  .expand-text {
    font-size: 12px;
  }
}

/* Enhanced chart container for desktop */
@media (min-width: 969px) {
  .chart-container {
    height: 350px;
  }
  
  .modal-chart-container {
    height: 500px;
  }
}

/* Temperature mode indicators */
.temperature-control label {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

/* Animation for chart appearance */
.chart-container {
  animation: chartFadeIn 0.8s ease-out;
}

@keyframes chartFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Temperature control animations */
.temperature-control {
  animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Model selection section layout */
.model-selection-section {
  animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Model variant selection styles */
.model-variant-info {
  margin-bottom: 15px;
}

.variant-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;
}

.variant-option {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.variant-option:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.variant-option.select-all-option {
  border-color: #17a2b8;
  background: linear-gradient(135deg, #d1ecf1 0%, #ffffff 100%);
}

.variant-option.select-all-option:hover {
  border-color: #17a2b8;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
}

.variant-option input[type="checkbox"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: #007bff;
}

.select-all-option input[type="checkbox"] {
  accent-color: #17a2b8;
}

.variant-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.variant-emoji {
  font-size: 24px;
  margin-right: 12px;
}

.variant-text {
  display: flex;
  flex-direction: column;
}

.variant-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.variant-description {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.selection-summary {
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  margin-top: 10px;
}

.selection-summary small {
  color: #495057;
  font-weight: 500;
}

/* Ensemble and model display styles */
.ensemble-info {
  background: linear-gradient(135deg, #d4edda 0%, #f8f9fa 100%);
  border-radius: 8px;
  padding: 8px 12px;
  margin: 8px 0;
}

.ensemble-badge {
  display: inline-block;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.models-used {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 6px;
}

.model-tag {
  display: inline-block;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
}

/* Responsive design for model variants */
@media (max-width: 768px) {
  .variant-options {
    gap: 8px;
  }

  .variant-option {
    padding: 10px;
  }

  .variant-emoji {
    font-size: 20px;
    margin-right: 10px;
  }

  .variant-name {
    font-size: 13px;
  }

  .variant-description {
    font-size: 11px;
  }
}

/* Header Authentication Styles */
.header-content {
  position: relative;
}

.header-main {
  text-align: center;
}

.header-auth {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 10;
}

.auth-buttons {
  display: flex;
  gap: 8px;
}

.btn-secondary {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow: 0 2px 8px rgba(118, 75, 162, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(118, 75, 162, 0.5);
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-menu-trigger {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.4);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.user-menu-trigger:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.user-menu-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(20px);
  z-index: 1000;
  min-width: 160px;
  overflow: hidden;
}

.menu-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #495057;
  font-weight: 500;
  font-size: 14px;
}

.menu-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  transform: translateX(4px);
}

/* Authentication Modals */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.auth-modal {
  background: white;
  border-radius: 8px;
  padding: 0;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.auth-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.auth-modal-header h2 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.auth-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: #666;
  font-size: 12px;
}

.auth-switch {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
  color: #666;
}

.link-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  text-decoration: underline;
}

.link-btn:hover {
  color: #0056b3;
}

/* Query History Modal */
.history-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.history-modal {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.history-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.history-modal-header h2 {
  margin: 0;
  color: #333;
}

.history-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.history-stats {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.history-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background: white;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.model-type {
  background-color: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.timestamp {
  color: #666;
  font-size: 12px;
}

.history-item-text {
  margin-bottom: 10px;
  font-style: italic;
  color: #555;
}

.history-item-result {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.prediction {
  font-weight: 500;
}

.confidence {
  font-weight: 500;
}

.language, .processing-time {
  color: #666;
  font-size: 12px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.pagination-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.pagination-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.no-history {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Responsive design for authentication */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .header-auth {
    position: static;
    justify-content: center;
    margin-top: 15px;
  }

  .auth-buttons {
    gap: 6px;
  }

  .btn-secondary,
  .btn-primary {
    padding: 6px 12px;
    font-size: 12px;
  }

  .user-menu-trigger {
    padding: 6px 12px;
    font-size: 12px;
    max-width: 150px;
  }

  .auth-modal {
    width: 95%;
    max-width: 350px;
  }

  .history-modal {
    width: 95%;
    max-height: 85vh;
  }

  .history-item-result {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* Responsive design for larger screens */
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
    padding: 0 20px;
  }
}